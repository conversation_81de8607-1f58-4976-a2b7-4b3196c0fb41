#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.dretta<PERSON>@inria.fr
#

import torch
from scene import Scene
import os
from tqdm import tqdm
from os import makedirs
from gaussian_renderer import render, network_gui
import torchvision
from utils.general_utils import safe_state
from argparse import ArgumentParser
from arguments import ModelParams, PipelineParams, get_combined_args
from gaussian_renderer import GaussianModel
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import pearsonr
try:
    from diff_gaussian_rasterization import SparseGaussianAdam
    SPARSE_ADAM_AVAILABLE = True
except:
    SPARSE_ADAM_AVAILABLE = False

def analyze_features_rest(shs, save_dir="analysis_results"):
    """
    分析_features_rest属性并保存结果图片:
    1. 通道间相关性分布（使用余弦相似度）
    2. 稀疏性分布（使用L1/L2比率）
    
    参数:
        shs: 形状为[num_gaussians, 3, 15]的张量
        save_dir: 结果保存目录
    返回:
        cosine_sims: 所有高斯点的通道间余弦相似度 [n, 3, 3]
        sparse_measures: 所有高斯点的稀疏性度量 [n]
    """
    os.makedirs(save_dir, exist_ok=True)
    device = shs.device
    num_gaussians = shs.shape[0]
    
    # 1. 计算通道间余弦相似度
    print("计算通道间相关性...")
    with torch.no_grad():
        # 归一化处理
        norms = torch.norm(shs, dim=2, keepdim=True)
        normalized = shs / norms  # [n, 3, 15]
        
        # 批量计算余弦相似度矩阵
        cosine_sim = torch.bmm(normalized, normalized.transpose(1, 2))  # [n, 3, 3]
        
        # 提取非对角线元素
        triu_indices = torch.triu_indices(3, 3, offset=1).to(device)
        channel_corrs = cosine_sim[:, triu_indices[0], triu_indices[1]].flatten().cpu().numpy()
    
    # 2. 计算稀疏性度量
    print("计算稀疏性...")
    with torch.no_grad():
        # 重组为[num_gaussians, 45]的矩阵
        flattened = shs.reshape(num_gaussians, -1)
        
        # 计算L1/L2比率
        l1_norms = torch.norm(flattened, p=1, dim=1)
        l2_norms = torch.norm(flattened, p=2, dim=1)
        sparse_measures = (l1_norms / l2_norms).cpu().numpy()
        abs_vals = flattened.abs()
        sorted_vals = torch.sort(abs_vals, dim=1, descending=True).values
        top15_ratio = (sorted_vals[:, :15].sum(dim=1) / (sorted_vals.sum(dim=1))).cpu().numpy()
        
        # 新增Gini系数
        sorted_vals_asc = torch.sort(abs_vals, dim=1).values  # 升序排列
        cumsum = torch.cumsum(sorted_vals_asc, dim=1)
        n = flattened.shape[1]
        gini = 1 - 2 * torch.sum(cumsum / (cumsum[:, -1:] + 1e-8) * 
                       (torch.arange(n, device=device) + 0.5) / n, dim=1).cpu().numpy()
    # 3. 保存可视化结果
    plt.figure(figsize=(12, 5))
    
    # 通道相关性分布
    plt.subplot(2, 2, 1)
    plt.hist(channel_corrs, bins=50, alpha=0.7, range=(-1, 1))
    plt.title('Channel Cosine Similarity Distribution')
    plt.xlabel('Cosine Similarity')
    plt.ylabel('Frequency')
    plt.grid(True, linestyle='--', alpha=0.5)
    
    # 稀疏性分布
    plt.subplot(2, 2, 2)
    plt.hist(sparse_measures, bins=50, alpha=0.7)
    plt.title('Per-Gaussian Sparsity Distribution')
    plt.xlabel('Sparsity Measure (L1/L2)')
    plt.ylabel('Frequency')
    plt.grid(True, linestyle='--', alpha=0.5)
    
    plt.subplot(2, 2, 3)
    plt.hist(top15_ratio, bins=50, alpha=0.7, range=(0, 1))
    plt.title('Top-15 Energy Ratio')
    plt.xlabel('Energy Ratio')
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.tight_layout()
    
    
    plt.subplot(2, 2, 4)
    plt.hist(gini, bins=50, alpha=0.7, range=(0, 1))
    plt.title('Gini Coefficient')
    plt.xlabel('gini')
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.tight_layout()
    
    plt.savefig(os.path.join(save_dir, 'features_rest_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 保存原始数据
    np.save(os.path.join(save_dir, 'cosine_similarities.npy'), cosine_sim.cpu().numpy())
    np.save(os.path.join(save_dir, 'sparsity_measures.npy'), sparse_measures)
    
    print(f"分析结果已保存至: {save_dir}")
    
    abs_mean = shs.abs().mean(dim=1).cpu().numpy()  # 先绝对值再通道平均
    
    # 2. 计算每个阶数的值占总值的比例 [n,15]
    total_values = abs_mean.sum(axis=1, keepdims=True)
    value_ratios = abs_mean / (total_values + 1e-8)  # 避免除以零
    
    # 3. 计算各阶的比例分布（均值±标准差）
    mean_ratio = np.mean(value_ratios, axis=0) * 100  # 转为百分比
    std_ratio = np.std(value_ratios, axis=0) * 100
    
    # 4. 绘图
    plt.figure(figsize=(10, 4))
    bars = plt.bar(range(1, 16), mean_ratio, 
                 yerr=std_ratio,  # 添加误差条
                 capsize=3, 
                 alpha=0.7,
                 color='skyblue')
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.1f}%',
                ha='center', va='bottom')
    
    plt.title('Coefficient Value Distribution by SH Order (%)')
    plt.xlabel('SH Order (1-15)')
    plt.ylabel('Percentage of Total Value')
    plt.xticks(range(1, 16))
    plt.ylim(0, max(mean_ratio)*1.2)
    plt.grid(axis='y', linestyle='--', alpha=0.3)
    plt.savefig(os.path.join(save_dir, 'sh_analysis.png'), dpi=150, bbox_inches='tight')
    plt.close()

        
    return cosine_sim.cpu().numpy(), sparse_measures

def get_distribution(dataset: ModelParams, iteration: int):
    with torch.no_grad():
        gaussians = GaussianModel(dataset.sh_degree)
        scene = Scene(dataset, gaussians, load_iteration=iteration, shuffle=False)
        shs = gaussians._features_rest  # shape: [num_gaussians, 3, 15]
        shs = shs.transpose(1, 2)
        # 分析_features_rest属性
        channel_corrs, sparse_measures = analyze_features_rest(shs)
        
        return channel_corrs, sparse_measures
        

if __name__ == "__main__":
    # Set up command line argument parser
    parser = ArgumentParser(description="Testing script parameters")
    model = ModelParams(parser, sentinel=True)
    pipeline = PipelineParams(parser)
    parser.add_argument("--iteration", default=-1, type=int)
    parser.add_argument("--skip_train", action="store_true")
    parser.add_argument("--skip_test", action="store_true")
    parser.add_argument("--quiet", action="store_true")
    args = get_combined_args(parser)
    print("Rendering " + args.model_path)

    # Initialize system state (RNG)
    safe_state(args.quiet)

    get_distribution(model.extract(args), args.iteration)