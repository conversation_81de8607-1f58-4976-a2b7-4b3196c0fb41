#!/bin/bash

# Path to your Python script
PYTHON_SCRIPT="./train_imp_score_spherical_gaussian_cullSG.py"  
BASE_DATASET_DIR="../Feature_Gaussian_Compression/mip360"
chkpnt_iter=14999
declare -a run_scenes=(
   "bicycle"
  # "bonsai"
  # "counter"
  # "kitchen"
  # "room"
  #"stump"
  # "garden"
  # "train"
  # "truck"
  # "treehill"
  # "playroom"
  # "dr<PERSON><PERSON><PERSON>"
)


PORT="1244"
SPA_INTERVAL="50"

# Function to get the id of an available GPU
get_available_gpu() {
  local mem_threshold=2000
  nvidia-smi --query-gpu=index,memory.used --format=csv,noheader,nounits | \
    awk -v threshold="$mem_threshold" -F', ' '
      $2 < threshold { print $1; exit }
    '
}



run_script(){
  while ss -tuln | grep -q ":$PORT";do
    echo "Port $PORT is in use."
    PORT=$((PORT + 1))
    echo "New port number is $PORT"
  done

  local DATASET_DIR=$1
  local DATASET_NAME=$(basename "$DATASET_DIR")
  local SPA_RATIO1=0.5
  local SPA_RATIO2=0.72
  local SPA_START_ITER=25200
  local SPA_STOP_ITER=35200
  local SPA_SG_START_ITER=35500
  local SPA_SG_STOP_ITER=45500
  local sharpness_threshold=0.005
  OUTPUT_DIR="./output/"$DATASET_NAME"/sg_cull/1200_15000for"$SPA_RATIO1"_"$SPA_START_ITER"-"$SPA_STOP_ITER"for"$SPA_RATIO2"_"$SPA_SG_START_ITER"-"$SPA_SG_STOP_ITER"for"$sharpness_threshold""
  echo "Output script for $OUTPUT_DIR"
  mkdir -p "$OUTPUT_DIR"
  ckpt="$OUTPUT_DIR"/chkpnt"$chkpnt_iter".pth
  SPA_RATIO=0.72
  if [ -f "$OUTPUT_DIR/$OUTPUT_FILE" ]; then
      echo "Output file $OUTPUT_FILE already exists. Skipping this iteration."
      continue
  fi

  gpu_id=$(get_available_gpu)
  if [[ -n $gpu_id ]]; then
    echo "GPU $gpu_id is available."
    CUDA_VISIBLE_DEVICES=$gpu_id python "$PYTHON_SCRIPT" \
    --port "$PORT" \
    -s="$DATASET_DIR" \
    -m="$OUTPUT_DIR" \
    --eval \
    --prune_ratio1 "$SPA_RATIO1"\
    --prune_ratio2 "$SPA_RATIO2"\
    --imp_metric "outdoor"\
    --iterations "50000" \
    -i images_4 \
    --checkpoint_iterations 50000 \
    --optimizing_spa_start_iter $SPA_START_ITER \
    --optimizing_spa_stop_iter $SPA_STOP_ITER \
    --optimizing_spa_sg_start_iter $SPA_SG_START_ITER \
    --optimizing_spa_sg_stop_iter $SPA_SG_STOP_ITER \
    --sharpness_threshold $sharpness_threshold
    #--start_checkpoint "$ckpt"
    else
      echo "No GPU available at the moment. Retrying in 1 minute."
      sleep 60
  fi
}

for view in "${run_scenes[@]}"; do
    echo "Running script for $view"
    run_script "$BASE_DATASET_DIR/$view"
done